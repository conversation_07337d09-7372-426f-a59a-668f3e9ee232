.login-container {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }
}

.logo-section {
  position: relative;
  z-index: 1;
  text-align: center;
  color: white;
  max-width: 500px;
}

.logo {
  margin-bottom: 32px;
  
  img {
    width: 80px;
    height: 80px;
    filter: brightness(0) invert(1);
  }
}

.brand-title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 16px;
  background: linear-gradient(45deg, #fff, #e8f4fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 20px;
  margin-bottom: 48px;
  opacity: 0.9;
  font-weight: 300;
}

.feature-list {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  font-size: 16px;
  opacity: 0.9;
  
  .feature-icon {
    font-size: 24px;
    margin-right: 16px;
    width: 32px;
    text-align: center;
  }
}

.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  padding: 60px;
  min-width: 500px;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 48px;
  
  h2 {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #1a1a1a;
  }
  
  p {
    color: #666;
    font-size: 16px;
  }
}

.login-form {
  .ant-form-item {
    margin-bottom: 24px;
  }
  
  .ant-input,
  .ant-input-password {
    height: 48px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    padding: 0 16px;
    
    &:focus,
    &:hover {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }
  }
  
  .site-form-item-icon {
    color: #999;
  }
}

.login-form-forgot {
  float: right;
  color: #667eea;
  text-decoration: none;
  
  &:hover {
    color: #764ba2;
  }
}

.login-form-button {
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-size: 16px;
  font-weight: 500;
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.login-tips {
  text-align: center;
  margin-top: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-left {
    padding: 40px 20px;
    min-height: 40vh;
    
    .brand-title {
      font-size: 36px;
    }
    
    .brand-subtitle {
      font-size: 18px;
    }
    
    .feature-list {
      display: none;
    }
  }
  
  .login-right {
    padding: 40px 20px;
    min-width: auto;
  }
  
  .login-form-container {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .login-left {
    padding: 20px;
    
    .brand-title {
      font-size: 28px;
    }
    
    .brand-subtitle {
      font-size: 16px;
    }
  }
  
  .login-right {
    padding: 20px;
  }
}
