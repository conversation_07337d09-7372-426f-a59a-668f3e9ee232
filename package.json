{"name": "react-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "clean": "<PERSON><PERSON><PERSON> dist", "analyze": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"antd": "^5.26.2", "axios": "^1.10.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.24.1"}, "devDependencies": {"@iconify/utils": "^2.3.0", "@types/node": "^24.0.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@unocss/reset": "^0.61.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.6", "prettier": "^3.6.1", "rimraf": "^5.0.7", "sass": "^1.89.2", "typescript": "^5.2.2", "unocss": "^0.61.3", "vite": "^5.3.4"}}