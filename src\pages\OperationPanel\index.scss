.operation-panel {
  position: relative;
  width: 100%;
  height: calc(100vh - 64px); // 减去Header高度
  background-color: #000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: white;

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1000;
  }
}

.background-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.top-toolbar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10;

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .frame-info {
    font-size: 14px;
    font-weight: bold;
  }
}

.left-panel, .right-panel {
  position: absolute;
  top: 80px;
  bottom: 80px;
  width: 280px;
  padding: 16px;
  background: rgba(44, 62, 80, 0.8);
  border-radius: 8px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.left-panel {
  left: 16px;
}

.right-panel {
  right: 16px;
}

.info-card, .control-card {
  background: rgba(52, 73, 94, 0.9) !important;
  border: 1px solid #4a6572;
  color: white;

  .ant-card-head {
    background: rgba(34, 47, 62, 0.9);
    color: white;
    border-bottom: 1px solid #4a6572;
  }

  .ant-card-body {
    padding: 12px;
  }
}

.object-info, .status-info, .control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .info-item, .status-item {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
  }

  .label, .status-label, .control-label {
    color: #bdc3c7;
  }

  .value {
    font-weight: bold;
  }
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
}

.bottom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 12px 24px;
  background: rgba(0, 0, 0, 0.7);
  z-index: 10;
  gap: 24px;
}

.playback-controls {
  display: flex;
  gap: 16px;
}

.timeline-container {
  flex-grow: 1;
}

.timeline-slider {
  .ant-slider-rail {
    background-color: #4a6572;
  }
  .ant-slider-track {
    background-color: #3498db;
  }
  .ant-slider-handle {
    border-color: #3498db;
  }
}

.frame-info-bottom {
  font-family: 'Courier New', Courier, monospace;
  font-size: 16px;
  min-width: 80px;
  text-align: center;
}

.floating-controls {
  .floating-btn-group {
    position: absolute;
    z-index: 20;
    display: flex;
    flex-direction: column;
    gap: 12px;

    &.top-left {
      top: 80px;
      left: 310px;
    }
    &.top-right {
      top: 80px;
      right: 310px;
    }
    &.bottom-right {
      bottom: 80px;
      right: 310px;
      flex-direction: row;
    }
  }
}
